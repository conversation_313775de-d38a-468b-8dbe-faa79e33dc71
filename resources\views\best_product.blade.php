<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Best Products - PT. Putera Wibowo Borneo</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    @vite([
        'resources/css/app.css',
        'resources/css/lazy-loading.css',
        'resources/js/app.js',
        'resources/js/lazy-loading.js'
    ])
    <style>
        html {
            background: none;
        }

        body {
            display: flex;
            flex-direction: column;
            height: 100vh;
            background-image:
                linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 80%),
                url('{{ asset('images/bg.png') }}');
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            overflow: hidden;
        }

        .page-container {
            flex: 1;
            padding: clamp(0.5rem, 2vw, 1rem);
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .header {
            margin-bottom: 1rem;
        }

        .right-logo {
            text-align: center;
        }

        .logo-line {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 0.5rem;
        }

        .logo-line img {
            width: 60px;
            height: 60px;
        }

        .slogan {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--purple);
            margin: 0;
        }

        .tagline {
            font-size: 1rem;
            color: var(--text-primary);
            font-style: italic;
        }

        .best-product-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .best-product-header {
            text-align: center;
            margin-bottom: 1rem;
        }

        .best-product-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--purple);
            margin: 0;
        }

        .search-container {
            margin-bottom: 1rem;
            text-align: center;
        }

        .search-input {
            width: 100%;
            max-width: 400px;
            padding: 0.75rem 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--purple);
            box-shadow: 0 0 0 3px rgba(108, 86, 123, 0.1);
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            padding: 1rem 0;
            overflow-y: auto;
            flex: 1;
        }

        .product-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .discount-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            background: #e74c3c;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .product-image {
            width: 100%;
            height: 150px;
            object-fit: contain;
            margin: 1rem 0;
        }

        .product-name {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--purple);
            margin-bottom: 0.5rem;
        }

        .product-details {
            margin-bottom: 1rem;
        }

        .product-brand, .product-type, .product-price {
            margin-bottom: 0.25rem;
            font-size: 0.9rem;
        }

        .product-brand {
            color: #666;
        }

        .product-type {
            color: #666;
        }

        .product-price {
            font-weight: 600;
            color: var(--purple);
            font-size: 1rem;
        }

        .product-info {
            margin-bottom: 1rem;
        }

        .discount-text {
            color: #27ae60;
            font-size: 0.85rem;
            margin-bottom: 0.25rem;
        }

        .warranty-text {
            color: #e74c3c;
            font-size: 0.85rem;
        }

        .view-more-btn {
            background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .view-more-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(74, 144, 226, 0.3);
        }

        .home-button {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: linear-gradient(135deg, var(--purple) 0%, var(--dark-blue) 100%);
            color: white;
            padding: 1rem;
            border-radius: 50px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            z-index: 1000;
        }

        .home-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            color: white;
            text-decoration: none;
        }

        .imgicon-purple {
            filter: brightness(0) invert(1);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .products-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1rem;
            }

            .best-product-title {
                font-size: 1.5rem;
            }

            .home-button {
                bottom: 1rem;
                right: 1rem;
                padding: 0.75rem;
            }

            .home-button span {
                display: none;
            }
        }

        @media (max-width: 576px) {
            .page-container {
                padding: 0.5rem;
            }

            .best-product-container {
                padding: 0.75rem;
            }

            .products-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <div class="page-container">
        <!-- Header -->
        <div class="header">
            <div class="right-logo">
                <div class="logo-line">
                    <img src="{{ asset('images/logo.png') }}" alt="Logo" />
                    <p class="slogan">PT. PUTERA WIBOWO BORNEO</p>
                </div>
                <div class="tagline">"Mitra Terpercaya dalam Solusi Alat Berat & Layanan Teknologi Industri"</div>
            </div>
        </div>

        <!-- Best Product Content -->
        <div class="best-product-container">
            <div class="best-product-header">
                <h1 class="best-product-title">BEST PRODUCTS</h1>
            </div>

            <!-- Search -->
            <div class="search-container">
                <form method="GET" action="{{ route('best_product.index') }}">
                    <input type="text" name="search" class="search-input" placeholder="Cari Nama Part..." 
                           value="{{ $search ?? '' }}">
                </form>
            </div>

            <!-- Products Grid -->
            <div class="products-grid">
                @forelse($bestProducts as $product)
                <div class="product-card">
                    <div class="discount-badge">{{ $product['discount'] }} Off</div>
                    
                    <img src="{{ asset($product['image']) }}" alt="{{ $product['name'] }}" class="product-image">
                    
                    <div class="product-name">{{ $product['name'] }}</div>
                    
                    <div class="product-details">
                        <div class="product-brand">Merek : {{ $product['brand'] }}</div>
                        <div class="product-type">Type : {{ $product['type'] }}</div>
                        <div class="product-price">Harga : {{ $product['price'] }}</div>
                    </div>
                    
                    <div class="product-info">
                        <div class="discount-text">{{ $product['discount_text'] }}</div>
                        <div class="warranty-text">{{ $product['warranty'] }}</div>
                    </div>
                    
                    <button class="view-more-btn">Lihat Merek Lainnya</button>
                </div>
                @empty
                <div class="col-12 text-center py-4">
                    <em>Tidak ada produk yang ditemukan.</em>
                </div>
                @endforelse
            </div>
        </div>
    </div>

    <!-- Home Button -->
    <a href="{{ route('home') }}" class="home-button">
        <img class="imgicon-purple" src="{{ asset('assets/icon/home.png') }}" alt="Home" width="40" height="40">
        <span>HOME</span>
    </a>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
