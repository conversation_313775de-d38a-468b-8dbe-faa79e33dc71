/**
 * Lazy Loading Functionality
 * Displays a loading spinner while the page is rendering and hides it once fully loaded
 */

(function() {
    'use strict';

    // Configuration
    const config = {
        minLoadingTime: 500, // Minimum time to show loading (in ms)
        fadeOutDuration: 300, // Fade out animation duration (in ms)
        checkInterval: 50, // Interval to check if page is loaded (in ms)
        maxWaitTime: 10000 // Maximum time to wait before hiding loader (in ms)
    };

    // State tracking
    let loadingStartTime = Date.now();
    let isPageLoaded = false;
    let isImagesLoaded = false;
    let checkTimeout = null;

    /**
     * Create and inject the loading overlay HTML
     */
    function createLoadingOverlay() {
        const overlay = document.createElement('div');
        overlay.className = 'lazy-loading-overlay';
        overlay.innerHTML = `
            <div class="lazy-loading-spinner-container">
                <div class="lazy-loading-spinner purple-theme"></div>
                <p class="lazy-loading-text">Memuat halaman...</p>
            </div>
        `;
        
        // Insert at the beginning of body to ensure it's on top
        document.body.insertBefore(overlay, document.body.firstChild);
        return overlay;
    }

    /**
     * Check if all images are loaded
     */
    function checkImagesLoaded() {
        const images = document.querySelectorAll('img');
        let loadedCount = 0;
        
        if (images.length === 0) {
            isImagesLoaded = true;
            return true;
        }

        images.forEach(img => {
            if (img.complete && img.naturalHeight !== 0) {
                loadedCount++;
            }
        });

        isImagesLoaded = loadedCount === images.length;
        return isImagesLoaded;
    }

    /**
     * Check if page is fully loaded
     */
    function checkPageLoaded() {
        // Check document ready state
        const isDocumentReady = document.readyState === 'complete';
        
        // Check if all images are loaded
        const areImagesLoaded = checkImagesLoaded();
        
        // Check if minimum loading time has passed
        const hasMinTimeElapsed = (Date.now() - loadingStartTime) >= config.minLoadingTime;
        
        return isDocumentReady && areImagesLoaded && hasMinTimeElapsed;
    }

    /**
     * Hide the loading overlay with fade out animation
     */
    function hideLoadingOverlay() {
        const overlay = document.querySelector('.lazy-loading-overlay');
        if (!overlay) return;

        // Add fade out class
        overlay.classList.add('fade-out');
        
        // Add class to body to indicate page is loaded
        document.body.classList.add('page-loaded');
        
        // Remove overlay after animation completes
        setTimeout(() => {
            if (overlay && overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, config.fadeOutDuration);

        // Clear any pending checks
        if (checkTimeout) {
            clearTimeout(checkTimeout);
            checkTimeout = null;
        }
    }

    /**
     * Continuously check if page is loaded
     */
    function startLoadingCheck() {
        function checkAndHide() {
            if (checkPageLoaded()) {
                hideLoadingOverlay();
                return;
            }

            // Continue checking if max wait time hasn't been reached
            if ((Date.now() - loadingStartTime) < config.maxWaitTime) {
                checkTimeout = setTimeout(checkAndHide, config.checkInterval);
            } else {
                // Force hide after max wait time
                hideLoadingOverlay();
            }
        }

        checkAndHide();
    }

    /**
     * Initialize lazy loading
     */
    function initLazyLoading() {
        // Create and show loading overlay immediately
        createLoadingOverlay();
        
        // Start checking for page load completion
        startLoadingCheck();

        // Backup: Hide loading on window load event
        window.addEventListener('load', function() {
            setTimeout(hideLoadingOverlay, Math.max(0, config.minLoadingTime - (Date.now() - loadingStartTime)));
        });

        // Backup: Hide loading on DOMContentLoaded if images are already loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                if (checkImagesLoaded()) {
                    setTimeout(hideLoadingOverlay, Math.max(0, config.minLoadingTime - (Date.now() - loadingStartTime)));
                }
            });
        }
    }

    // Initialize immediately if DOM is already loaded, otherwise wait for DOMContentLoaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initLazyLoading);
    } else {
        initLazyLoading();
    }

    // Expose function globally for manual control if needed
    window.hideLazyLoading = hideLoadingOverlay;

})();
