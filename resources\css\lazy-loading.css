/* Lazy Loading Overlay Styles */
.lazy-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(2px);
    transition: opacity 0.3s ease-out;
}

.lazy-loading-overlay.fade-out {
    opacity: 0;
    pointer-events: none;
}

/* Loading Spinner Container */
.lazy-loading-spinner-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

/* Main Spinner */
.lazy-loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #e0e7ff;
    border-top: 4px solid #6c567b;
    border-radius: 50%;
    animation: lazy-loading-spin 1s linear infinite;
}

/* Loading Text */
.lazy-loading-text {
    font-family: Helvetica, sans-serif;
    font-size: 1rem;
    color: #0b0643;
    font-weight: 500;
    text-align: center;
    margin: 0;
}

/* Spinner Animation */
@keyframes lazy-loading-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Pulse Animation for Text */
.lazy-loading-text {
    animation: lazy-loading-pulse 2s ease-in-out infinite;
}

@keyframes lazy-loading-pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

/* Alternative Spinner Styles for Different Pages */
.lazy-loading-spinner.purple-theme {
    border-top-color: #6c567b;
}

.lazy-loading-spinner.blue-theme {
    border-top-color: #394867;
}

.lazy-loading-spinner.red-theme {
    border-top-color: #e85a65;
}

/* Responsive Design */
@media (max-width: 768px) {
    .lazy-loading-spinner {
        width: 50px;
        height: 50px;
        border-width: 3px;
    }
    
    .lazy-loading-text {
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .lazy-loading-spinner {
        width: 40px;
        height: 40px;
        border-width: 3px;
    }
    
    .lazy-loading-text {
        font-size: 0.8rem;
    }
}

/* Ensure overlay doesn't interfere with existing elements */
.lazy-loading-overlay * {
    box-sizing: border-box;
}

/* Hide overlay when page is fully loaded */
body.page-loaded .lazy-loading-overlay {
    display: none;
}
