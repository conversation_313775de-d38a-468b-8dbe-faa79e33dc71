<?php

namespace App\Http\Controllers\BestProduct;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class BestProductController extends Controller
{
    /**
     * Display the best products page.
     */
    public function index(Request $request)
    {
        // Sample best product data - in a real application, this would come from a database
        $bestProducts = collect([
            [
                'id' => 1,
                'name' => 'COMPRESSOR',
                'brand' => 'SANNY',
                'type' => 'SY215 STD',
                'price' => 'Rp. 6.030.000',
                'discount' => '50%',
                'discount_text' => 'Diskon : 50 % Hingga September',
                'warranty' => 'Warranty : 3 Bulan',
                'status' => 'Lainnya',
                'image' => 'images/compressor1.png'
            ],
            [
                'id' => 2,
                'name' => 'COMPRESSOR',
                'brand' => 'SANNY',
                'type' => 'SY215 STD',
                'price' => 'Rp. 6.030.000',
                'discount' => '30%',
                'discount_text' => 'Diskon : 30 % Hingga September',
                'warranty' => 'Warranty : 3 Bulan',
                'status' => 'Lainnya',
                'image' => 'images/compressor2.png'
            ],
            [
                'id' => 3,
                'name' => 'COMPRESSOR',
                'brand' => 'SANNY',
                'type' => 'SY215 STD',
                'price' => 'Rp. 6.030.000',
                'discount' => '30%',
                'discount_text' => 'Diskon : 30 % Hingga September',
                'warranty' => 'Warranty : 3 Bulan',
                'status' => 'Lainnya',
                'image' => 'images/compressor3.png'
            ],
            [
                'id' => 4,
                'name' => 'COMPRESSOR',
                'brand' => 'SANNY',
                'type' => 'SY215 STD',
                'price' => 'Rp. 6.030.000',
                'discount' => '30%',
                'discount_text' => 'Diskon : 30 % Hingga September',
                'warranty' => 'Warranty : 3 Bulan',
                'status' => 'Lainnya',
                'image' => 'images/compressor4.png'
            ]
        ]);

        // Handle search functionality
        $search = $request->get('search');
        if ($search) {
            $bestProducts = $bestProducts->filter(function ($product) use ($search) {
                return stripos($product['name'], $search) !== false ||
                       stripos($product['brand'], $search) !== false ||
                       stripos($product['type'], $search) !== false;
            });
        }

        return view('best_product', compact('bestProducts', 'search'));
    }

    /**
     * Show the form for creating a new product.
     */
    public function create()
    {
        return view('best_product.create');
    }

    /**
     * Store a newly created product in storage.
     */
    public function store(Request $request)
    {
        // Implementation for storing product
        return redirect()->route('best_product.index')->with('success', 'Product created successfully.');
    }

    /**
     * Display the specified product.
     */
    public function show($id)
    {
        // Implementation for showing specific product
        return view('best_product.show', compact('id'));
    }

    /**
     * Show the form for editing the specified product.
     */
    public function edit($id)
    {
        // Implementation for editing product
        return view('best_product.edit', compact('id'));
    }

    /**
     * Update the specified product in storage.
     */
    public function update(Request $request, $id)
    {
        // Implementation for updating product
        return redirect()->route('best_product.index')->with('success', 'Product updated successfully.');
    }

    /**
     * Remove the specified product from storage.
     */
    public function destroy($id)
    {
        // Implementation for deleting product
        return redirect()->route('best_product.index')->with('success', 'Product deleted successfully.');
    }
}
