import { defineConfig } from "vite";
import laravel from "laravel-vite-plugin";

export default defineConfig({
    plugins: [
        laravel({
            input: [
                "resources/css/app.css",
                "resources/css/calender.css",

                // CSS Home
                "resources/css/home/<USER>",

                // CSS Profile
                "resources/css/profile.css",

                // CSS Layanan
                "resources/css/layanan.css",

                // CSS Lazy Loading
                "resources/css/lazy-loading.css",

                // JS HOme
                "resources/js/home/<USER>",

                // JS APP
                "resources/js/app.js",

                // JS Lazy Loading
                "resources/js/lazy-loading.js",
            ],
            refresh: true,
        }),
    ],
    // server: {
    //     host: true,
    // },
});
